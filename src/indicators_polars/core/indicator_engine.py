"""
Main indicator calculation engine with partitioning and parallel processing
"""

import logging
import polars as pl
from typing import Dict, Any, List, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime
import time

from pytz import timezone
import pytz

from .config_manager import ConfigManager
from ..indicators.technical_indicators import TechnicalIndicators
from ..indicators.custom_indicators import CustomIndicators
from ..models.schemas import DataValidator
from ..utils.monitoring import PerformanceMonitor


logger = logging.getLogger(__name__)


class IndicatorEngine:
    """Main engine for calculating indicators with partitioning support"""
    
    def __init__(self, config_manager: ConfigManager, performance_monitor: PerformanceMonitor):
        self.config = config_manager
        self.monitor = performance_monitor
        
        # Initialize indicator calculators
        self.technical_indicators = TechnicalIndicators(config_manager.technical_indicators)
        self.custom_indicators = CustomIndicators(config_manager.custom_indicators)
        
        # Data validator
        self.validator = DataValidator()
    
    def calculate_indicators(
        self, 
        df: pl.DataFrame, 
        delta_only: bool = False,
        existing_data: Optional[pl.DataFrame] = None
    ) -> pl.DataFrame:
        """
        Calculate indicators for the input DataFrame
        
        Args:
            df: Input DataFrame with market data
            delta_only: If True, only calculate for new data
            existing_data: Previously calculated data for delta mode
        """
        if df.is_empty():
            logger.info("No data to process")
            return pl.DataFrame()
        
        with self.monitor.track_operation("calculate_indicators", len(df)) as metrics:
            try:
                # Validate input data
                # logger.info(f"Top 5 rows of delta_volume before validate_input_data: {df.head(5)[['timestamp', 'delta_volume']]}")
                df = self.validator.validate_input_data(df)
                
                # logger.info(f"Top 5 rows of delta_volume after validate_input_data: {df.head(5)[['timestamp', 'delta_volume']]}")
                # Create partitions
                partitions = self._create_partitions(df)
                logger.debug(f"Created {len(partitions)} partitions for processing")

                # Process partitions in parallel
                results = self._process_partitions_parallel(partitions, delta_only, existing_data)
                
                # Combine results
                if results:
                    final_result = pl.concat(results, how="vertical")
                    logger.debug(f"Combined results: {len(final_result)} rows")
                else:
                    final_result = self.validator.create_output_dataframe(df)
                
                metrics.rows_processed = len(final_result)
                return final_result
                
            except Exception as e:
                logger.error(f"Error in indicator calculation: {e}")
                metrics.errors_count += 1
                raise
    
    def _create_partitions(self, df: pl.DataFrame) -> Dict[str, pl.DataFrame]:
        """Create partitions based on symbol, expiry_date, expiry_type, and strike"""
        partitions = {}
        partition_columns = self.validator.get_partition_columns()
        
        # Group by partition columns
        grouped = df.group_by(partition_columns, maintain_order=True)
        
        for group_key, group_df in grouped:
            # Create partition key
            if isinstance(group_key, tuple):
                key_dict = dict(zip(partition_columns, group_key))
            else:
                key_dict = {partition_columns[0]: group_key}
            
            partition_key = self.validator.create_partition_key(key_dict)
            partitions[partition_key] = group_df
            
            logger.debug(f"Created partition {partition_key} with {len(group_df)} rows")
        
        return partitions
    
    def _process_partitions_parallel(
        self, 
        partitions: Dict[str, pl.DataFrame],
        delta_only: bool,
        existing_data: Optional[pl.DataFrame]
    ) -> List[pl.DataFrame]:
        """Process partitions in parallel using ThreadPoolExecutor"""
        results = []
        max_workers = min(self.config.processing.workers, len(partitions))
        
        logger.debug(f"Processing {len(partitions)} partitions with {max_workers} workers")
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks
            future_to_partition = {}
            for partition_key, partition_df in partitions.items():
                future = executor.submit(
                    self._process_single_partition,
                    partition_key,
                    partition_df,
                    delta_only,
                    existing_data
                )
                future_to_partition[future] = partition_key
            
            # Collect results
            for future in as_completed(future_to_partition):
                partition_key = future_to_partition[future]
                try:
                    result_df = future.result()
                    if not result_df.is_empty():
                        results.append(result_df)
                    logger.debug(f"Completed partition {partition_key}")
                except Exception as e:
                    logger.error(f"Error processing partition {partition_key}: {e}")
        
        return results
    
    def _process_single_partition(
        self,
        partition_key: str,
        df: pl.DataFrame,
        delta_only: bool,
        existing_data: Optional[pl.DataFrame]
    ) -> pl.DataFrame:
        """Process a single partition"""
        start_time = time.time()
        
        try:
            logger.debug(f"Processing partition {partition_key} with {len(df)} rows")
            
            # Sort by timestamp for proper time series calculation
            df = df.sort("timestamp")
            
            # For delta mode, we might need to combine with existing data for proper calculation
            if delta_only and existing_data is not None:
                logger.debug(f"Combining existing data for delta calculation")
                # Filter existing data for this partition
                partition_columns = self.validator.get_partition_columns()
                
                # Extract partition values from key
                partition_values = partition_key.split("|")
                partition_filter = pl.lit(True)
                
                for i, col in enumerate(partition_columns):
                    if i < len(partition_values):
                        value = partition_values[i]
                        # Handle numeric values
                        try:
                            if col == 'strike':
                                value = float(value)
                            partition_filter = partition_filter & (pl.col(col) == value)
                        except ValueError:
                            partition_filter = partition_filter & (pl.col(col) == value)
                
                existing_partition = existing_data.filter(partition_filter)
                
                if not existing_partition.is_empty():
                    # Combine existing and new data for calculation context
                    combined_df = pl.concat([existing_partition, df], how="vertical").sort("timestamp")
                    
                    # Calculate indicators on combined data
                    result_df = self._calculate_indicators_for_partition(combined_df, partition_key)
                    
                    # Return only the new rows
                    new_timestamps = df.select("timestamp").to_series()
                    result_df = result_df.filter(pl.col("timestamp").is_in(new_timestamps))
                else:
                    # No existing data, calculate normally
                    result_df = self._calculate_indicators_for_partition(df, partition_key)
            else:
                logger.debug(f"Delta mode is False or existing_data is None. Processing normally")
                # Normal calculation
                result_df = self._calculate_indicators_for_partition(df, partition_key)
            
            duration = time.time() - start_time
            logger.debug(f"Partition {partition_key} processed in {duration:.2f}s")
            
            return result_df
            
        except Exception as e:
            logger.error(f"Error processing partition {partition_key}: {e}")
            return pl.DataFrame()
    
    def _calculate_indicators_for_partition(self, df: pl.DataFrame, partition_key: str) -> pl.DataFrame:
        """Calculate all indicators for a single partition"""
        if df.is_empty():
            return df

        # Check minimum data requirements
        min_data_points = 50  # Minimum data points for meaningful indicators
        if len(df) < min_data_points:
            logger.debug(f"Partition {partition_key} has insufficient data: {len(df)} < {min_data_points}")
            # Still process but expect some indicators to be null

        # Create output DataFrame with proper schema
        result_df = self.validator.create_output_dataframe(df)

        # Calculate technical indicators
        result_df = self.technical_indicators.calculate_for_partition(result_df, partition_key)

        # Calculate custom indicators
        result_df = self.custom_indicators.calculate_for_partition(result_df, partition_key)

        return result_df
    
    def get_all_indicator_columns(self) -> List[str]:
        """Get list of all indicator columns that will be generated"""
        columns = []
        columns.extend(self.technical_indicators.get_indicator_columns())
        columns.extend(self.custom_indicators.get_indicator_columns())
        return columns
    
    def calculate_historical_progressive(
        self, 
        df: pl.DataFrame, 
        start_time: str = "09:15",
        end_time: str = "15:30"
    ) -> pl.DataFrame:
        """
        Calculate indicators with progressing windows from start of day
        
        This simulates the historical progression where indicators are calculated
        at each timestamp with data available up to that point.
        """
        if df.is_empty():
            return df
        
        logger.info("Starting historical progressive calculation")
        
        # Sort by timestamp
        df = df.sort("timestamp")
        
        # Get unique timestamps
        timestamps = df.select("timestamp").unique().sort("timestamp").to_series()
        
        results = []
        #print the type of each columns in df
        # for col in df.columns:
        #     logger.info(f"Column {col} type: {df[col].dtype}")
        # # logger.info(f"Columns in df: {df.columns}")

        for i, current_timestamp in enumerate(timestamps):
            # Get data up to current timestamp
            progressive_df = df.filter(pl.col("timestamp") <= current_timestamp)
            
            # Calculate indicators for this progressive window
            result_df = self.calculate_indicators(progressive_df, delta_only=False)
            logger.debug(f"Top 5 rows of delta_volume after calculate_indicators: {result_df.head(5)[['timestamp', 'delta_volume']]}")
            logger.debug(f"Progressive calculation for {current_timestamp}: {len(result_df)} rows")
            # Keep only the current timestamp results (handle timezone comparison)
            try:
                logger.debug(f"Current timestamp: {current_timestamp}")
                current_timestamp = current_timestamp.astimezone(pytz.UTC).replace(tzinfo=None).replace(tzinfo=None)
                current_result = result_df.filter(
                    #remote the timezone similar to current_timestamp.replace(tzinfo=None)
                    pl.col("timestamp") == current_timestamp
                )
                logger.debug(f"Current result: {len(current_result)} rows")
                # current_result = result_df.filter(pl.col("timestamp") == current_timestamp)
            except Exception as e:
                logger.exception(f"Exception = {e}")
                logger.warning(f"Failed to filter by timestamp: {current_timestamp}. Trying naive comparison...")
                # Handle timezone mismatch by converting to naive datetime
                current_result = result_df.filter(
                    pl.col("timestamp").dt.replace_time_zone(None) ==
                    (current_timestamp.replace(tzinfo=None) if hasattr(current_timestamp, 'tzinfo') and current_timestamp.tzinfo else current_timestamp)
                )
            
            if not current_result.is_empty():
                results.append(current_result)
            
            if (i + 1) % 10 == 0:
                logger.info(f"Processed {i + 1}/{len(timestamps)} timestamps")
        
        if results:
            final_result = pl.concat(results, how="vertical")
            logger.info(f"Historical progressive calculation completed: {len(final_result)} rows")
            return final_result
        else:
            return pl.DataFrame()
