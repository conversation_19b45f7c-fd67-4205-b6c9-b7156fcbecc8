"""
Technical indicators using polars_talib
"""

import logging
import polars as pl
import polars_talib as ta
from typing import Dict, Any, List
from ..core.config_manager import TechnicalIndicatorConfig


logger = logging.getLogger(__name__)


class TechnicalIndicators:
    """Calculate technical indicators using polars_talib"""
    
    def __init__(self, config: TechnicalIndicatorConfig):
        self.config = config
    
    def calculate_all(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate all enabled technical indicators"""
        if df.is_empty():
            return df
        
        logger.debug(f"Calculating technical indicators for {len(df)} rows")
        
        result_df = df.clone()
        
        # Calculate RSI
        if self.config.rsi_enabled:
            result_df = self._calculate_rsi(result_df)
        
        # Calculate MACD
        if self.config.macd_enabled:
            result_df = self._calculate_macd(result_df)
        
        # Calculate SMA
        if self.config.sma_enabled:
            result_df = self._calculate_sma(result_df)
        
        # Calculate EMA
        if self.config.ema_enabled:
            result_df = self._calculate_ema(result_df)
        
        logger.debug("Technical indicators calculation completed")
        return result_df
    
    def _calculate_rsi(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate RSI for specified columns"""
        for column in self.config.rsi_columns:
            if column in df.columns:
                try:
                    rsi_col_name = f"rsi_{column}"
                    df = df.with_columns(
                        ta.rsi(pl.col(column), timeperiod=self.config.rsi_period).alias(rsi_col_name)
                    )
                    logger.debug(f"Calculated RSI for column {column}")
                except Exception as e:
                    logger.warning(f"Failed to calculate RSI for column {column}: {e}")
        
        return df
    
    def _calculate_macd(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate MACD for specified columns"""
        # Check if we have enough data for MACD calculation
        min_required = max(self.config.macd_slow_period, self.config.macd_signal_period) + 10
        if len(df) < min_required:
            logger.debug(f"Insufficient data for MACD calculation: {len(df)} < {min_required}")
            # Add null MACD columns
            for column in self.config.macd_columns:
                if column in df.columns:
                    df = df.with_columns([
                        pl.lit(0, dtype=pl.Float64).alias(f"macd_{column}"),
                        pl.lit(0, dtype=pl.Float64).alias(f"macd_signal_{column}"),
                        pl.lit(0, dtype=pl.Float64).alias(f"macd_histogram_{column}")
                    ])
            return df

        for column in self.config.macd_columns:
            if column in df.columns:
                try:
                    macd_col = f"macd_{column}"
                    signal_col = f"macd_signal_{column}"
                    histogram_col = f"macd_histogram_{column}"

                    # Calculate MACD components
                    macd_result = ta.macd(
                        pl.col(column),
                        fastperiod=self.config.macd_fast_period,
                        slowperiod=self.config.macd_slow_period,
                        signalperiod=self.config.macd_signal_period
                    )

                    df = df.with_columns([
                        macd_result.struct.field("macd").alias(macd_col),
                        macd_result.struct.field("macdsignal").alias(signal_col),
                        macd_result.struct.field("macdhist").alias(histogram_col)
                    ])

                    logger.debug(f"Calculated MACD for column {column}")
                except Exception as e:
                    logger.warning(f"Failed to calculate MACD for column {column}: {e}")
                    # Add null columns on error
                    df = df.with_columns([
                        pl.lit(0, dtype=pl.Float64).alias(f"macd_{column}"),
                        pl.lit(0, dtype=pl.Float64).alias(f"macd_signal_{column}"),
                        pl.lit(0, dtype=pl.Float64).alias(f"macd_histogram_{column}")
                    ])

        return df
    
    def _calculate_sma(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate Simple Moving Average for specified columns and periods"""
        for column in self.config.sma_columns:
            if column in df.columns:
                for period in self.config.sma_periods:
                    try:
                        sma_col_name = f"sma_{period}_{column}"
                        df = df.with_columns(
                            ta.sma(pl.col(column), timeperiod=period).alias(sma_col_name)
                        )
                        logger.debug(f"Calculated SMA({period}) for column {column}")
                    except Exception as e:
                        logger.warning(f"Failed to calculate SMA({period}) for column {column}: {e}")
        
        return df
    
    def _calculate_ema(self, df: pl.DataFrame) -> pl.DataFrame:
        """Calculate Exponential Moving Average for specified columns and periods"""
        for column in self.config.ema_columns:
            if column in df.columns:
                for period in self.config.ema_periods:
                    try:
                        ema_col_name = f"ema_{period}_{column}"
                        df = df.with_columns(
                            ta.ema(pl.col(column), timeperiod=period).alias(ema_col_name)
                        )
                        logger.debug(f"Calculated EMA({period}) for column {column}")
                    except Exception as e:
                        logger.warning(f"Failed to calculate EMA({period}) for column {column}: {e}")
        
        return df
    
    def calculate_for_partition(self, df: pl.DataFrame, partition_key: str) -> pl.DataFrame:
        """Calculate technical indicators for a specific partition"""
        if df.is_empty():
            return df
        
        logger.debug(f"Calculating technical indicators for partition: {partition_key}")
        
        # Sort by timestamp to ensure proper time series order
        df = df.sort("timestamp")
        
        # Calculate indicators
        result_df = self.calculate_all(df)
        
        return result_df
    
    def get_indicator_columns(self) -> List[str]:
        """Get list of all indicator column names that will be generated"""
        columns = []
        
        # RSI columns
        if self.config.rsi_enabled:
            for column in self.config.rsi_columns:
                columns.append(f"rsi_{column}")
        
        # MACD columns
        if self.config.macd_enabled:
            for column in self.config.macd_columns:
                columns.extend([
                    f"macd_{column}",
                    f"macd_signal_{column}",
                    f"macd_histogram_{column}"
                ])
        
        # SMA columns
        if self.config.sma_enabled:
            for column in self.config.sma_columns:
                for period in self.config.sma_periods:
                    columns.append(f"sma_{period}_{column}")
        
        # EMA columns
        if self.config.ema_enabled:
            for column in self.config.ema_columns:
                for period in self.config.ema_periods:
                    columns.append(f"ema_{period}_{column}")
        
        return columns
