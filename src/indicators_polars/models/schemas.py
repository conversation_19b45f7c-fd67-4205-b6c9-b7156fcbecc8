"""
Polars schemas for ClickHouse table structures and data validation
"""

import polars as pl
from typing import Dict, Any, List
from enum import Enum


class SymbolEnum(Enum):
    """Symbol enumeration matching ClickHouse enum"""
    UNKNOWN = 0
    NIFTY = 1
    BANKNIFTY = 2
    FINNIFTY = 3
    MIDCPNIFTY = 4


class ExpiryTypeEnum(Enum):
    """Expiry type enumeration matching ClickHouse enum"""
    UNKNOWN = 0
    CE = 1
    PE = 2
    FUTURE = 3


class StrikeTypeEnum(Enum):
    """Strike type enumeration matching ClickHouse enum"""
    UNKNOWN = 0
    NEAR = 1
    FAR = 2
    FUTURE = 3


class MoneynessEnum(Enum):
    """Moneyness enumeration matching ClickHouse enum"""
    UNKNOWN = 0
    ITM = 1
    OTM = 2
    DOTM = 3
    DITM = 4
    ATM = 5


# Input data schema (from ClickHouse source table)
INPUT_SCHEMA = {
    'symbol': pl.Utf8,
    'timestamp': pl.Datetime,
    'expiry_date': pl.Date,
    'current_price': pl.Float32,
    'strike': pl.Float32,
    'strike_type': pl.Utf8,
    'lot_size': pl.UInt16,
    'expiry_type': pl.Utf8,
    'moneyness': pl.Utf8,
    'last_price': pl.Float32,
    'ltp_change': pl.Float32,
    'oi': pl.Float32,
    'oi_change': pl.Float32,
    'volume': pl.Float32,
    'is_liquid': pl.UInt8,
    'theta': pl.Float32,
    'delta': pl.Float32,
    'gamma': pl.Float32,
    'vega': pl.Float32,
    'iv': pl.Float32,
    'batch': pl.UInt32,
    'prev_current_price': pl.Float32,
    'prev_last_price': pl.Float32,
    'prev_oi': pl.Float32,
    'prev_volume': pl.Float32,
    'prev_delta': pl.Float32,
    'prev_gamma': pl.Float32,
    'prev_iv': pl.Float32,
    'delta_current_price': pl.Float32,
    'delta_last_price': pl.Float32,
    'delta_oi': pl.Float32,
    'delta_volume': pl.Float32,
    'delta_delta': pl.Float32,
    'delta_gamma': pl.Float32,
    'delta_iv': pl.Float32
}

# Output schema for indicators
OUTPUT_SCHEMA = {
    # Key columns
    'symbol': pl.Utf8,
    'timestamp': pl.Datetime,
    'expiry_date': pl.Date,
    'strike': pl.Float32,
    'expiry_type': pl.Utf8,
    
    # Original data (key columns)
    'current_price': pl.Float32,
    'last_price': pl.Float32,
    'oi': pl.Float32,
    'volume': pl.Float32,
    'delta_volume': pl.Float32,
    'delta': pl.Float32,
    'gamma': pl.Float32,
    'theta': pl.Float32,
    'vega': pl.Float32,
    'iv': pl.Float32,
    
    # Technical indicators
    'rsi_last_price': pl.Float32,
    'rsi_current_price': pl.Float32,
    'macd_last_price': pl.Float32,
    'macd_signal_last_price': pl.Float32,
    'macd_histogram_last_price': pl.Float32,
    'sma_5_last_price': pl.Float32,
    'sma_10_last_price': pl.Float32,
    'sma_20_last_price': pl.Float32,
    'sma_50_last_price': pl.Float32,
    'ema_5_last_price': pl.Float32,
    'ema_10_last_price': pl.Float32,
    'ema_20_last_price': pl.Float32,
    'ema_50_last_price': pl.Float32,
    
    # Ranking indicators
    'oi_rank': pl.Float32,
    'gamma_rank': pl.Float32,
    'delta_rank': pl.Float32,
    'theta_rank': pl.Float32,
    'vega_rank': pl.Float32,
    'iv_rank': pl.Float32,
    'delta_volume_rank': pl.Float32,

    'oi_per_rank': pl.Float32,
    'gamma_per_rank': pl.Float32,
    'delta_per_rank': pl.Float32,
    'theta_per_rank': pl.Float32,
    'vega_per_rank': pl.Float32,
    'iv_per_rank': pl.Float32,
    'delta_volume_per_rank': pl.Float32,
    
    # Custom indicators
    'intrinsic_value': pl.Float32,
    'intrinsic_value_rank': pl.Float32,
    'intrinsic_value_per_rank': pl.Float32,
    'premium': pl.Float32,
    'premium_rank': pl.Float32,
    'premium_per_rank': pl.Float32,

    # Pattern detection (boolean flags as strings)
    'consecutive_price_drop': pl.Utf8,
    'consecutive_oi_build': pl.Utf8,
    'candle_dip': pl.Utf8,
    'candle_rise': pl.Utf8,
    
    # Trading signals
    'buy_signal_1': pl.Utf8,
    'buy_signal_2': pl.Utf8,
    'buy_signal_3': pl.Utf8,
    'buy_signal_4': pl.Utf8,
    'buy_signal_5': pl.Utf8,
    'sell_signal_1': pl.Utf8,
    'sell_signal_2': pl.Utf8,
    'sell_signal_3': pl.Utf8,
    'sell_signal_4': pl.Utf8,
    'sell_signal_5': pl.Utf8,
    
    # Signal counts
    'standard_buy_count': pl.UInt8,
    'special_buy_count': pl.UInt8,
    'sell_count': pl.UInt8,
    
    # Special conditions
    'gamma_move': pl.Utf8,
    'iv_vega_condition': pl.Utf8,
    'gamma_condition': pl.Utf8,
    
    # Metadata
    'calculation_timestamp': pl.Datetime
}


class DataValidator:
    """Validate and transform data according to schemas"""
    
    @staticmethod
    def validate_input_data(df: pl.DataFrame) -> pl.DataFrame:
        """Validate and cast input data to correct types"""
        if df.is_empty():
            return df
        
        # Check required columns
        required_columns = ['symbol', 'timestamp', 'expiry_date', 'strike', 'expiry_type']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")
        
        # Cast columns to correct types where possible
        for col, dtype in INPUT_SCHEMA.items():
            if col in df.columns:
                try:
                    df = df.with_columns(pl.col(col).cast(dtype, strict=False))
                except Exception as e:
                    # Log warning but continue
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Could not cast column {col} to {dtype}: {e}")
        
        return df
    
    @staticmethod
    def create_output_dataframe(input_df: pl.DataFrame) -> pl.DataFrame:
        """Create output DataFrame with proper schema"""
        if input_df.is_empty():
            return pl.DataFrame(schema=OUTPUT_SCHEMA)
        
        # Start with key columns from input
        key_columns = ['symbol', 'timestamp', 'expiry_date', 'strike', 'expiry_type']
        data_columns = ['current_price', 'last_price', 'oi', 'volume', 'delta_volume','delta', 'gamma', 'theta', 'vega', 'iv']
        
        # Select existing columns
        existing_columns = [col for col in key_columns + data_columns if col in input_df.columns]
        output_df = input_df.select(existing_columns)
        
        # Add missing columns with null values
        for col, dtype in OUTPUT_SCHEMA.items():
            if col not in output_df.columns:
                output_df = output_df.with_columns(pl.lit(0).cast(dtype).alias(col))
        
        # Add calculation timestamp
        from datetime import datetime
        output_df = output_df.with_columns(
            pl.lit(datetime.now()).alias('calculation_timestamp')
        )
        
        return output_df.select(list(OUTPUT_SCHEMA.keys()))
    
    @staticmethod
    def get_partition_columns() -> List[str]:
        """Get columns used for partitioning data"""
        return ['symbol', 'expiry_date', 'expiry_type', 'strike']
    
    @staticmethod
    def create_partition_key(row: Dict[str, Any]) -> str:
        """Create partition key from row data"""
        partition_cols = DataValidator.get_partition_columns()
        key_parts = []
        
        for col in partition_cols:
            value = row.get(col, 'UNKNOWN')
            if isinstance(value, float):
                value = f"{value:.2f}"
            key_parts.append(str(value))
        
        return "|".join(key_parts)
