"""
Basic functionality tests for IndicatorsUsingPolars
"""

import sys
import unittest
from pathlib import Path
from datetime import datetime, date
import polars as pl

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / 'src'))

from indicators_polars.utils.config_loader import Config<PERSON>oader
from indicators_polars.core.config_manager import ConfigManager
from indicators_polars.models.schemas import DataValidator, INPUT_SCHEMA
from indicators_polars.indicators.technical_indicators import TechnicalIndicators
from indicators_polars.indicators.custom_indicators import CustomIndicators


class TestBasicFunctionality(unittest.TestCase):
    """Test basic functionality of the indicators system"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Load test configuration
        config_path = Path(__file__).parent.parent / 'config.yaml'
        self.config_loader = ConfigLoader(str(config_path))
        self.config_dict = self.config_loader.load()
        self.config_manager = ConfigManager(self.config_dict)
        
        # Create sample data
        self.sample_data = self._create_sample_data()
    
    def _create_sample_data(self) -> pl.DataFrame:
        """Create sample market data for testing"""
        timestamps = [
            datetime(2025, 1, 13, 9, 15),
            datetime(2025, 1, 13, 9, 16),
            datetime(2025, 1, 13, 9, 17),
            datetime(2025, 1, 13, 9, 18),
            datetime(2025, 1, 13, 9, 19),
        ]
        
        data = {
            'symbol': ['NIFTY'] * 5,
            'timestamp': timestamps,
            'expiry_date': [date(2025, 1, 16)] * 5,
            'current_price': [23500.0, 23510.0, 23505.0, 23520.0, 23515.0],
            'strike': [23500.0] * 5,
            'strike_type': ['NEAR'] * 5,
            'lot_size': [25] * 5,
            'expiry_type': ['CE'] * 5,
            'moneyness': ['ATM'] * 5,
            'last_price': [100.0, 105.0, 102.0, 108.0, 106.0],
            'ltp_change': [0.0, 5.0, -3.0, 6.0, -2.0],
            'oi': [1000.0, 1050.0, 1100.0, 1150.0, 1200.0],
            'oi_change': [0.0, 50.0, 50.0, 50.0, 50.0],
            'volume': [100.0, 150.0, 120.0, 180.0, 160.0],
            'is_liquid': [1] * 5,
            'theta': [-0.5, -0.52, -0.51, -0.53, -0.52],
            'delta': [0.5, 0.52, 0.51, 0.53, 0.52],
            'gamma': [0.01, 0.012, 0.011, 0.013, 0.012],
            'vega': [0.1, 0.11, 0.105, 0.115, 0.11],
            'iv': [0.2, 0.21, 0.205, 0.215, 0.21],
            'batch': [1] * 5,
            'prev_current_price': [23490.0, 23500.0, 23510.0, 23505.0, 23520.0],
            'prev_last_price': [95.0, 100.0, 105.0, 102.0, 108.0],
            'prev_oi': [950.0, 1000.0, 1050.0, 1100.0, 1150.0],
            'prev_volume': [80.0, 100.0, 150.0, 120.0, 180.0],
            'prev_delta': [0.48, 0.5, 0.52, 0.51, 0.53],
            'prev_gamma': [0.009, 0.01, 0.012, 0.011, 0.013],
            'prev_iv': [0.19, 0.2, 0.21, 0.205, 0.215],
            'delta_current_price': [10.0, 10.0, -5.0, 15.0, -5.0],
            'delta_last_price': [5.0, 5.0, -3.0, 6.0, -2.0],
            'delta_oi': [50.0, 50.0, 50.0, 50.0, 50.0],
            'delta_volume': [20.0, 50.0, -30.0, 60.0, -20.0],
            'delta_delta': [0.02, 0.02, -0.01, 0.02, -0.01],
            'delta_gamma': [0.001, 0.002, -0.001, 0.002, -0.001],
            'delta_iv': [0.01, 0.01, -0.005, 0.01, -0.005]
        }
        
        return pl.DataFrame(data)
    
    def test_config_loading(self):
        """Test configuration loading"""
        self.assertIsNotNone(self.config_dict)
        self.assertIn('database', self.config_dict)
        self.assertIn('market', self.config_dict)
        self.assertIn('processing', self.config_dict)
        self.assertIn('indicators', self.config_dict)
    
    def test_config_manager(self):
        """Test configuration manager"""
        self.assertTrue(self.config_manager.validate())
        self.assertIsNotNone(self.config_manager.database)
        self.assertIsNotNone(self.config_manager.market)
        self.assertIsNotNone(self.config_manager.processing)
        self.assertIsNotNone(self.config_manager.technical_indicators)
        self.assertIsNotNone(self.config_manager.custom_indicators)
    
    def test_data_validator(self):
        """Test data validation"""
        validator = DataValidator()
        
        # Test input validation
        validated_data = validator.validate_input_data(self.sample_data)
        self.assertFalse(validated_data.is_empty())
        
        # Test output DataFrame creation
        output_df = validator.create_output_dataframe(validated_data)
        self.assertFalse(output_df.is_empty())
        
        # Test partition key creation
        row_dict = validated_data.row(0, named=True)
        partition_key = validator.create_partition_key(row_dict)
        self.assertIsInstance(partition_key, str)
        self.assertIn('NIFTY', partition_key)
    
    def test_technical_indicators(self):
        """Test technical indicators calculation"""
        tech_indicators = TechnicalIndicators(self.config_manager.technical_indicators)
        
        # Test indicator calculation
        result_df = tech_indicators.calculate_all(self.sample_data)
        self.assertFalse(result_df.is_empty())
        
        # Check if RSI columns are added
        if self.config_manager.technical_indicators.rsi_enabled:
            rsi_columns = [f"rsi_{col}" for col in self.config_manager.technical_indicators.rsi_columns]
            for col in rsi_columns:
                if col in result_df.columns:
                    self.assertIn(col, result_df.columns)
        
        # Test partition calculation
        partition_result = tech_indicators.calculate_for_partition(self.sample_data, "test_partition")
        self.assertFalse(partition_result.is_empty())
    
    def test_custom_indicators(self):
        """Test custom indicators calculation"""
        custom_indicators = CustomIndicators(self.config_manager.custom_indicators)
        
        # Test indicator calculation
        result_df = custom_indicators.calculate_all(self.sample_data)
        self.assertFalse(result_df.is_empty())
        
        # Check if intrinsic value is calculated
        if self.config_manager.custom_indicators.intrinsic_value_enabled:
            self.assertIn('intrinsic_value', result_df.columns)
        
        # Test partition calculation
        partition_result = custom_indicators.calculate_for_partition(self.sample_data, "test_partition")
        self.assertFalse(partition_result.is_empty())
    
    def test_market_time_validation(self):
        """Test market time validation"""
        # Test market hours
        market_time = datetime(2025, 1, 13, 10, 30)  # 10:30 AM
        self.assertTrue(self.config_manager.is_market_time(market_time))
        
        # Test outside market hours
        outside_time = datetime(2025, 1, 13, 8, 30)  # 8:30 AM
        self.assertFalse(self.config_manager.is_market_time(outside_time))
    
    def test_processing_date(self):
        """Test processing date calculation"""
        processing_date = self.config_manager.get_processing_date()
        self.assertIsInstance(processing_date, date)
    
    def test_indicator_columns_list(self):
        """Test getting list of indicator columns"""
        tech_indicators = TechnicalIndicators(self.config_manager.technical_indicators)
        custom_indicators = CustomIndicators(self.config_manager.custom_indicators)
        
        tech_columns = tech_indicators.get_indicator_columns()
        custom_columns = custom_indicators.get_indicator_columns()
        
        self.assertIsInstance(tech_columns, list)
        self.assertIsInstance(custom_columns, list)
        
        # Should have some columns if indicators are enabled
        if self.config_manager.technical_indicators.rsi_enabled:
            self.assertGreater(len(tech_columns), 0)
        
        if self.config_manager.custom_indicators.rankings_enabled:
            self.assertGreater(len(custom_columns), 0)


def run_tests():
    """Run all tests"""
    unittest.main(verbosity=2)


if __name__ == '__main__':
    run_tests()
