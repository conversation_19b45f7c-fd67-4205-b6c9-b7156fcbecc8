# IndicatorsUsingPolars Configuration

# Database Configuration
database:
  clickhouse:
    host: "localhost"
    port: 8123
    username: "scorepandit"
    password: "Score@1615"
    database: "sensibull"
    table_prefix: "sensibull_index_"
    # table_prefix: "indicator_input_test_"
    connection_pool_size: 10
    query_timeout: 30

# Market Configuration
market:
  timezone: "Asia/Kolkata"
  start_time: "09:15"
  end_time: "15:30"
  symbols: ["NIFTY"]  # Testing with single symbol
  expiry_types: ["CE"]  # Testing with PE only
  strikes: [24500]  # Testing with specific strike
  top_expiries_per_symbol: 1  # Process only top 2 expiries per symbol
  expiry_filter:
    current_week: true
    next_week: true
    additional_weeks: 0

# Processing Configuration
processing:
  mode: "live"  # "live" or "historical"
  workers: 4
  batch_size: 10000
  max_processing_time_seconds: 50  # Must be less than 60 for live mode
  
  # Historical mode specific
  historical:
    start_date: "2025-01-13"  # Format: YYYY-MM-DD
    end_date: null  # null means current date
  
  # Live mode specific
  live:
    polling_interval_seconds: 60
    delta_only: true  # Only process new/changed data

# Indicators Configuration
indicators:
  # Standard Technical Indicators
  technical:
    rsi:
      enabled: true
      period: 14
      columns: ["last_price", "current_price"]
    
    macd:
      enabled: true
      fast_period: 12
      slow_period: 26
      signal_period: 9
      columns: ["last_price"]
    
    sma:
      enabled: true
      periods: [5, 10, 20, 50]
      columns: ["last_price", "oi", "volume"]
    
    ema:
      enabled: true
      periods: [5, 10, 20, 50]
      columns: ["last_price", "oi", "volume"]
  
  # Custom Excel-based Indicators
  custom:
    # Ranking indicators (PERCENTRANK equivalent)
    rankings:
      enabled: true
      columns:
        - "oi"
        - "gamma"
        - "delta"
        - "theta"
        - "vega"
        - "iv"
        - "delta_volume"
        - "premium"
        - "intrinsic_value"
      window_size: 100  # Rolling window for percentile calculation
      # Ranking methods per column (min, max, ordinal)
      ranking_methods:
        oi: "min"
        gamma: "min"
        delta: "min"
        theta: "min"
        vega: "min"
        iv: "min"
        delta_volume: "min"
        premium: "max"
        intrinsic_value: "max"
    
    # Intrinsic value calculation
    intrinsic_value:
      enabled: true
    
    # Pattern detection
    patterns:
      enabled: true
      consecutive_periods: 3  # For consecutive price drops, OI builds etc.
    
    # Trading signals
    trading_signals:
      enabled: true
      buy_signals: ["BUY1", "BUY2", "BUY3", "BUY4", "BUY5"]
      sell_signals: ["SELL1", "SELL2", "SELL3", "SELL4", "SELL5"]
      
      # Thresholds from Master sheet (these would be configurable)
      thresholds:
        candle_dip: -10
        candle_rise: 10
        int_value_rise_std: [70, 60]
        int_value_rise_ultra: [55, 35]
        int_value_drop: [40,50]
        premium_rise: [70,60]
        premium_drop: [8,10]
        gamma_rise: [85,60]
        del_vol_rise: [80,70]
        # iv_double_rise: [50, 60]
        # iv_triple_rise: [50,60,70]
        # iv_drop: [50,60,70]
        # delta_rise: [65,55,50]
        # delta_drop: [50,60,70]
        # theta_rise: [80,70,60]
        # theta_drop: [10,20,30]
        oi_rise: [70,67]
        oi_drop: [10,15]
        # vega_cool: [1,1,2]

# Output Configuration
output:
  clickhouse:
    table_name: "indicators_output"
    create_table: true
    batch_insert_size: 1000
  
  # Monitoring and alerting
  monitoring:
    enabled: true
    latency_threshold_seconds: 45
    error_threshold_count: 5

# Logging Configuration
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  console:
    enabled: true
    format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  file:
    enabled: true
    path: "logs/indicators.log"
    format: "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
    rotation:
      when: "midnight"
      interval: 1
      backup_count: 30
      encoding: "utf-8"
